import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { TradingStrategy } from '../strategies/TradingStrategy'

export class SignalEngine {
  private tradeSettings: TradeSettings = {
    confidenceThreshold: 0.4,
    profitMargin: 0.005,
    riskTolerance: 0.002,
    leverage: 1,
    stopLoss: 0.05,
    takeProfit: 0.1
  }

  private strategies: Strategy[] = [{ name: 'threshold' }]
  private currentSignal: Signal | null = null
  private candleCount: number = 0
  private strategyInstances: Record<string, TradingStrategy> = {}

  constructor(strategies: Strategy[], tradeSettings: TradeSettings) {
    this.strategies = strategies
    this.tradeSettings = { ...tradeSettings }
  }

  private strategyFactory = {
    threshold: () =>
      new ThresholdStrategy({
        threshold: this.tradeSettings.riskTolerance,
        minConfidence: this.tradeSettings.confidenceThreshold,
        candlePeriodSeconds: this.tradeSettings.candlePeriodSeconds,

        // ATR lookback of 14 bars
        atrPeriod: 14,

        // Scale ATR% up so that even modest wicks trigger
        // e.g. ATR ≈ 0.0003 (0.0007%) × 10 → 0.007% floor
        // Suggested Starting Range: Start with values between 0.2 and 0.5.
        // atrMultiplier: 0.5 means "trade if the move is at least 50% of the ATR."
        // atrMultiplier: 0.25 means "trade if the move is at least 25% of the ATR."
        atrMultiplier: 0.25,

        // Always require at least a 0.1% spike, even if ATR×multiplier is lower
        minThresholdPct: 0.001,

        volatilityFilter: true,
        momentumConfirmation: true,
        consistencyCheck: true,
        expiryToSeconds: this.tradeSettings.expirySeconds
      })
  }

  async generate(candle: Candle): Promise<Signal[]> {
    const strategies = this.getActiveStrategies()
    const results = await Promise.all(
      strategies.map((strategy) => this.runStrategy(strategy, candle))
    )
    const signals = results.filter((s): s is Signal => s !== null)

    const finalSignal = this.combineSignals(signals)

    if (finalSignal.confidence < this.tradeSettings.confidenceThreshold!) {
      finalSignal.action = 'HOLD'
      finalSignal.reason = `Confidence ${Math.round(finalSignal.confidence * 100)}% is below minimum ${this.tradeSettings.confidenceThreshold! * 100}%`
    }

    this.currentSignal = finalSignal
    return signals
  }

  getBestSignal(): Signal {
    return (
      this.currentSignal ?? {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    )
  }

  getCandleCount(): number {
    return this.candleCount
  }

  private getActiveStrategies(): Strategy[] {
    return this.strategies ?? []
  }

  private async runStrategy(strategy: Strategy, candle: Candle): Promise<Signal | null> {
    const factory = this.strategyFactory[strategy.name.toLowerCase()]
    if (!factory) return null

    const strategyInstance = this.getStrategyInstance(strategy.name.toLowerCase())
    const decision = await strategyInstance.evaluate(candle)

    this.candleCount = strategyInstance.getCandleCount()

    let action: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    if (decision.direction === 'high') action = 'BUY'
    else if (decision.direction === 'low') action = 'SELL'

    return {
      shouldTrade: decision.shouldTrade,
      action,
      reason: decision.reason ?? '',
      confidence: decision.confidence ?? 0,
      expirySeconds: decision.expirySeconds
    }
  }

  private combineSignals(signals: Signal[]): Signal {
    if (!signals.length) {
      return {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    }

    return signals.reduce((best, current) => {
      return current.confidence > best.confidence ? current : best
    })
  }

  private getStrategyInstance(name: string): TradingStrategy {
    if (!this.strategyInstances[name]) {
      this.strategyInstances[name] = this.strategyFactory[name]()
    }

    return this.strategyInstances[name]
  }
}
