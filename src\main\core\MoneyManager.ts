type TradeStrategy = 'flat' | 'martingale' | 'fibonacci'

interface TradeSession {
  tradeCapital: number
  targetProfit: number
  initialTradeAmount: number
  currentTradeAmount: number
  strategy: TradeStrategy

  // State tracking
  totalTrades: number
  winCount: number
  lossCount: number
  currentProfit: number
  remainingCapital: number

  // <PERSON><PERSON><PERSON><PERSON> sequence for strategy
  fibSequence: number[]
  fibIndex: number

  isActive: boolean
}

class MoneyManager {
  private accountBalance: number
  private session: TradeSession | null = null
  private sessionHistory: Array<{
    profitable: boolean
    profit: number
    trades: number
    wins: number
    losses: number
    strategy: TradeStrategy
    endTime: Date
  }> = []

  constructor(initialBalance: number = 1000) {
    this.accountBalance = initialBalance
  }

  startSession(
    tradeCapital: number,
    targetProfit: number,
    initialTradeAmount: number,
    strategy: TradeStrategy = 'flat'
  ): void {
    if (tradeCapital <= 0 || initialTradeAmount <= 0 || targetProfit <= 0) {
      throw new Error('All parameters must be positive numbers.')
    }
    if (initialTradeAmount > tradeCapital) {
      throw new Error('Initial trade amount cannot exceed trade capital.')
    }
    if (tradeCapital > this.accountBalance) {
      throw new Error('Insufficient account balance.')
    }

    this.session = {
      tradeCapital,
      targetProfit,
      initialTradeAmount,
      currentTradeAmount: initialTradeAmount,
      strategy,
      totalTrades: 0,
      winCount: 0,
      lossCount: 0,
      currentProfit: 0,
      remainingCapital: tradeCapital,
      fibSequence: [1, 1],
      fibIndex: 0,
      isActive: true
    }

    if (tradeCapital > this.accountBalance) {
      throw new Error('Insufficient account balance.')
    }

    this.accountBalance -= tradeCapital
  }

  placeTrade(win: boolean): void {
    if (!this.session || !this.session.isActive) return

    const s = this.session
    const amount = s.currentTradeAmount
    if (amount > s.remainingCapital) {
      this.endSession()
      return
    }

    s.remainingCapital -= amount
    s.totalTrades++

    if (win) {
      s.winCount++
      const profit = amount * 0.85
      s.currentProfit += profit
      s.remainingCapital += amount + profit

      if (s.strategy === 'fibonacci') s.fibIndex = 0
      s.currentTradeAmount = s.initialTradeAmount
    } else {
      s.lossCount++
      if (s.strategy === 'martingale') {
        s.currentTradeAmount *= 2
      } else if (s.strategy === 'fibonacci') {
        s.fibIndex++
        if (s.fibIndex >= s.fibSequence.length) {
          const next = s.fibSequence[s.fibIndex - 1] + s.fibSequence[s.fibIndex - 2]
          s.fibSequence.push(next)
        }
        s.currentTradeAmount = s.initialTradeAmount * s.fibSequence[s.fibIndex]
      }
    }

    // Stop if target or capital depleted
    if (s.currentProfit >= s.targetProfit || s.remainingCapital < s.currentTradeAmount) {
      this.endSession()
    }
  }

  endSession(): void {
    if (!this.session) return

    this.sessionHistory.push({
      profitable: this.session.currentProfit >= this.session.targetProfit,
      profit: this.session.currentProfit,
      trades: this.session.totalTrades,
      wins: this.session.winCount,
      losses: this.session.lossCount,
      strategy: this.session.strategy,
      endTime: new Date()
    })

    this.accountBalance += this.session.remainingCapital
    this.session.isActive = false
    this.session = null
  }

  getAccountBalance(): number {
    return this.accountBalance
  }

  getSessionHistory(): Array<{
    profitable: boolean
    profit: number
    trades: number
    wins: number
    losses: number
    strategy: TradeStrategy
    endTime: Date
  }> {
    return this.sessionHistory
  }

  isSessionActive(): boolean {
    return !!this.session?.isActive
  }
}

export default MoneyManager
