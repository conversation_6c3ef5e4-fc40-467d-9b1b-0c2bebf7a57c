export const formatData = (data: unknown[]): unknown[] | null => {
  if (Array.isArray(data) && data.length === 1) {
    try {
      const item = data[0]

      if (item instanceof Buffer || item instanceof ArrayBuffer) {
        const json = item.toString('utf-8')

        return JSON.parse(json)
      }

      return data
    } catch (err) {
      console.error(
        'Failed to format data',
        err instanceof Error ? `${err.message}\r\n${err.stack}` : `Unknown error`
      )
      return data
    }
  }

  return data
}

/**
 * Formats the expiry to the corresponding seconds
 * @param expiry - The expiry to format e.g. S30, M1, H1, etc.
 * @returns The corresponding seconds e.g. 30, 60, 3600, etc.
 */
export const expiryToSeconds = (expiry: string | number): number => {
  if (typeof expiry === 'number') return expiry

  const timeValue = parseInt(expiry.slice(1), 10)
  const periodType = expiry.charAt(0)

  switch (periodType) {
    case 'M':
      return timeValue * 60
    case 'S':
      return timeValue
    case 'H':
      return timeValue * 3600
    default:
      console.warn(`Invalid expiry format: ${expiry} - defaulting to 1 minute (M1)`)
      return 60 // Default to 1 minute if the format is invalid
  }
}

/**
 * Converts seconds back to expiry string format (inverse of expiryToSeconds)
 * @param seconds - The number of seconds to convert
 * @returns The expiry string in format like "S30", "M1", "H1", etc.
 */
export const periodToExpiry = (seconds: number): string => {
  // Handle edge cases
  if (seconds <= 0) {
    console.warn(`Invalid seconds value: ${seconds} - defaulting to S30`)
    return 'S30'
  }

  // Check if it's evenly divisible by hours (3600 seconds)
  if (seconds >= 3600 && seconds % 3600 === 0) {
    const hours = seconds / 3600
    return `H${hours}`
  }

  // Check if it's evenly divisible by minutes (60 seconds)
  if (seconds >= 60 && seconds % 60 === 0) {
    const minutes = seconds / 60
    return `M${minutes}`
  }

  // Otherwise, use seconds
  return `S${seconds}`
}

/**
 * Formats the chart period to the corresponding timeframe
 * @param chartPeriod - The chart period to format e.g. 0, 1, 2, etc.
 * @returns The corresponding timeframe e.g. S5, M1, H1, etc.
 */
export const formatChartTimeframe = (chartPeriod: number): string => {
  const chartPeriodMap: Record<number, string> = {
    0: `S5`,
    1: `S10`,
    2: `S15`,
    3: `S30`,
    4: `M1`,
    13: `M2`,
    14: `M3`,
    6: `M5`,
    7: `M10`,
    8: `M15`,
    9: `M30`,
    10: `H1`,
    11: `H4`,
    12: `D1`
  }

  return chartPeriodMap[chartPeriod]
}

/**
 * PocketOption sometimes gives seconds, sometimes ms.
 * If it’s <1e12 treat as seconds, otherwise as ms.
 */
export function toMs(time: number): number {
  // 1e12 ms ≈ Sat Nov 20 33658 17:46:40 GMT+0000,
  // so anything under that is almost certainly seconds
  return time < 1e12 ? time * 1000 : time
}

/**
 * Formats a time as "h:mm:ss AM/PM"
 * @param input – Date object, millisecond timestamp, or date-parsable string. Defaults to now.
 * @returns formatted time string
 */
export function formatTime(input?: Date | number | string): string {
  // normalize input into a Date
  const date = input instanceof Date ? input : new Date(input ?? Date.now())

  let hrs = date.getHours()
  const mins = String(date.getMinutes()).padStart(2, '0')
  const secs = String(date.getSeconds()).padStart(2, '0')
  const period = hrs >= 12 ? 'PM' : 'AM'

  // convert 0→12, 13→1, etc.
  hrs = hrs % 12 || 12

  return `${hrs}:${mins}:${secs} ${period}`
}

/**
 * Formats the timeframe to the corresponding offset
 * @param timeframe - The timeframe to format e.g. S5, M1, H1, etc.
 * @returns The corresponding offset e.g. 1000, 2000, 3000, etc.
 */
export const timeframeToOffset = (timeframe: string | number): string | number | undefined => {
  const timeframeMap: Record<string, number> = {
    S5: 1000,
    S10: 2000,
    S15: 3000,
    S30: 6000,
    M1: 9000,
    M2: 18000,
    M3: 27000,
    M5: 45000,
    M10: 90000,
    M15: 135000,
    M30: 270000,
    H1: 540000,
    H4: 2160000,
    D1: 12960000
  }
  if (typeof timeframe === 'string') return timeframeMap[timeframe]

  for (const [key, value] of Object.entries(timeframeMap)) {
    if (value === timeframe) return key
  }
  return undefined
}

export const formatAssets = (data: unknown[]): Asset[] => {
  try {
    if (Array.isArray(data) && data.length > 0 && data[0] instanceof Array) {
      return (data as RawAssetTuple[]).map(
        (item) =>
          ({
            /** 0 → Unique numerical identifier of the asset */
            id: item[0],
            /** 1 → Trading symbol, e.g. `#AAPL` */
            symbol: item[1],
            /** 2 → Human-readable name */
            name: item[2],
            /** 3 → Asset class/category (`currency`, `stock`, `index`, …) */
            category: item[3],
            /** 4 → Internal group identifier used by the platform */
            group: item[4],
            /** 5 → Current payout / profit percentage offered (e.g. 80 ⇒ 80 %) */
            profit: item[5],
            /** 6 → Minimum trade duration that can be selected (seconds) */
            minTime: item[6],
            /** 7 → Maximum trade duration that can be selected (seconds) */
            maxTime: item[7],
            /** 8 → Price precision (number of digits after the decimal separator) */
            precision: item[8],
            /** 9 → 1 if the symbol is OTC, 0 otherwise */
            isOTC: item[9],
            /** 10 → Identifier of the OTC twin (if regular market asset) */
            otcId: item[10],
            /** 11 → Identifier of the regular-market twin (if OTC asset) */
            regularId: item[11],
            /** 12 → Array with detailed trading schedule information */
            schedule: item[12],
            /** 13 → Timestamp when the current schedule starts (Unix seconds) */
            scheduleStart: item[13],
            /** 14 → Whether the asset is currently active/tradable */
            isActive: Boolean(item[14]),
            /** 15 → List of timeframes the asset supports on the chart */
            timeframes: item[15],
            /** 16 → Timestamp when the current schedule ends (Unix seconds) */
            scheduleEnd: item[16],
            /** 17 → Minimum amount allowed per trade */
            minAmount: item[17],
            /** 18 → Maximum amount allowed per trade */
            maxAmount: item[18]
          }) as Asset
      )
    }

    return data as Asset[]
  } catch (error) {
    console.debug(`Error formatting payload: ${error}`)
    return []
  }
}
